import { EntityDamageCause, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getTarget } from "../../general_mechanics/targetUtils";
/**
 * Attack timing constants for mid atomic attack
 */
const DAMAGE_TIMING = 50; // Apply damage at tick 50
const ANIMATION_TIME = 100; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes
/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
    /** Range of the atomic blast */
    RANGE: 14,
    /** Explosion radius */
    EXPLOSION_RADIUS: 5,
    /** Number of particles to spawn */
    PARTICLE_COUNT: 25
};
/**
 * Executes the mid atomic attack for the Void Hydra
 * Creates a large atomic explosion at the target's location
 *
 * @param voidHydra The void hydra entity
 */
export function executeMidAtomicAttack(voidHydra) {
    // Apply damage at tick 50
    let damageTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(damageTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "mid_atomic") {
                performAtomicExplosion(voidHydra);
            }
        }
        catch (error) {
            system.clearRun(damageTiming);
        }
    }, DAMAGE_TIMING);
    // Reset attack and start cooldown after animation completes
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "mid_atomic") {
                voidHydra.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    // End cooldown after cooldown time
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            voidHydra.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
/**
 * Performs the atomic explosion at the target's location
 * @param voidHydra The void hydra entity
 */
function performAtomicExplosion(voidHydra) {
    try {
        const target = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
        if (!target)
            return;
        const targetPos = target.location;
        const damage = VOID_HYDRA_ATTACK_DAMAGES.mid_atomic.damage;
        // Apply damage to entities in the explosion radius
        voidHydra.dimension
            .getEntities({
            location: targetPos,
            maxDistance: ATTACK_CONFIG.EXPLOSION_RADIUS,
            excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
            excludeFamilies: ["void_hydra", "boss"]
        })
            .forEach((entity) => {
            entity.applyDamage(damage, {
                cause: EntityDamageCause.entityAttack,
                damagingEntity: voidHydra
            });
            // Apply strong knockback
            try {
                const knockbackDirection = {
                    x: entity.location.x - targetPos.x,
                    y: 0.5,
                    z: entity.location.z - targetPos.z
                };
                const magnitude = Math.sqrt(knockbackDirection.x ** 2 + knockbackDirection.z ** 2);
                if (magnitude > 0) {
                    knockbackDirection.x = (knockbackDirection.x / magnitude) * 1.2;
                    knockbackDirection.z = (knockbackDirection.z / magnitude) * 1.2;
                }
                entity.applyKnockback(knockbackDirection.x, knockbackDirection.z, 1.2, 0.5);
            }
            catch (knockbackError) {
                try {
                    const impulse = {
                        x: (entity.location.x - targetPos.x) * 0.5,
                        y: 0.4,
                        z: (entity.location.z - targetPos.z) * 0.5
                    };
                    entity.applyImpulse(impulse);
                }
                catch (impulseError) {
                    // Ignore if both methods fail
                }
            }
        });
        // Spawn massive explosion effects
        voidHydra.dimension.spawnParticle("minecraft:huge_explosion_emitter", targetPos);
        // Spawn additional particles in expanding rings
        for (let ring = 1; ring <= 3; ring++) {
            const ringRadius = ring * (ATTACK_CONFIG.EXPLOSION_RADIUS / 3);
            const particlesInRing = Math.floor(ringRadius * 8);
            for (let i = 0; i < particlesInRing; i++) {
                const angle = (i / particlesInRing) * Math.PI * 2;
                const particlePos = {
                    x: targetPos.x + Math.cos(angle) * ringRadius,
                    y: targetPos.y + Math.random() * 3,
                    z: targetPos.z + Math.sin(angle) * ringRadius
                };
                voidHydra.dimension.spawnParticle("minecraft:dragon_breath_trail", particlePos);
            }
        }
    }
    catch (error) {
        // Handle errors silently
    }
}
