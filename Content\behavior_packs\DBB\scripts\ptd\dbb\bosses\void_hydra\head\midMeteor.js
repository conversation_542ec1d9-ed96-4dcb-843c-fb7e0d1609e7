import { EntityDamageCause, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getTarget } from "../../general_mechanics/targetUtils";
/**
 * Attack timing constants for mid meteor attack
 */
const METEOR_SPAWN_TIMING = 80; // Spawn meteors at tick 80
const ANIMATION_TIME = 160; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes
/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
    /** Number of meteors to spawn */
    METEOR_COUNT: 5,
    /** Range around target to spawn meteors */
    SPAWN_RANGE: 8,
    /** Explosion radius of each meteor */
    EXPLOSION_RADIUS: 3,
    /** Height from which meteors fall */
    METEOR_HEIGHT: 15
};
/**
 * Executes the mid meteor attack for the Void Hydra
 * Spawns multiple meteors that fall around the target area
 *
 * @param voidHydra The void hydra entity
 */
export function executeMidMeteorAttack(voidHydra) {
    // Spawn meteors at tick 80
    let meteorSpawnTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(meteorSpawnTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "mid_meteor") {
                spawnMeteors(voidHydra);
            }
        }
        catch (error) {
            system.clearRun(meteorSpawnTiming);
        }
    }, METEOR_SPAWN_TIMING);
    // Reset attack and start cooldown after animation completes
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "mid_meteor") {
                voidHydra.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    // End cooldown after cooldown time
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            voidHydra.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
/**
 * Spawns meteors around the target area
 * @param voidHydra The void hydra entity
 */
function spawnMeteors(voidHydra) {
    try {
        const target = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
        if (!target)
            return;
        const targetPos = target.location;
        const damage = VOID_HYDRA_ATTACK_DAMAGES.mid_meteor.damage;
        // Spawn multiple meteors around the target
        for (let i = 0; i < ATTACK_CONFIG.METEOR_COUNT; i++) {
            // Calculate random position around target
            const angle = (Math.PI * 2 * i) / ATTACK_CONFIG.METEOR_COUNT + (Math.random() - 0.5) * 0.5;
            const distance = Math.random() * ATTACK_CONFIG.SPAWN_RANGE;
            const meteorTargetPos = {
                x: targetPos.x + Math.cos(angle) * distance,
                y: targetPos.y,
                z: targetPos.z + Math.sin(angle) * distance
            };
            // Spawn meteor with delay based on index
            const delay = i * 5; // 5 tick delay between meteors
            system.runTimeout(() => {
                try {
                    const isDead = voidHydra.getProperty("ptd_dbb:dead");
                    if (isDead || voidHydra.getProperty("ptd_dbb:attack") !== "mid_meteor") {
                        return;
                    }
                    spawnSingleMeteor(voidHydra, meteorTargetPos, damage);
                }
                catch (error) {
                    // Handle errors silently
                }
            }, delay);
        }
    }
    catch (error) {
        // Handle errors silently
    }
}
/**
 * Spawns a single meteor at the specified location
 * @param voidHydra The void hydra entity
 * @param targetPos The position where the meteor should impact
 * @param damage The damage to deal
 */
function spawnSingleMeteor(voidHydra, targetPos, damage) {
    try {
        // Warning effect - spawn particles above the target area
        const warningPos = {
            x: targetPos.x,
            y: targetPos.y + ATTACK_CONFIG.METEOR_HEIGHT,
            z: targetPos.z
        };
        // Spawn warning particles
        voidHydra.dimension.spawnParticle("minecraft:lava_particle", warningPos);
        // Meteor impact after a short delay
        system.runTimeout(() => {
            try {
                const isDead = voidHydra.getProperty("ptd_dbb:dead");
                if (isDead)
                    return;
                // Create explosion effect
                voidHydra.dimension.spawnParticle("minecraft:huge_explosion_emitter", targetPos);
                // Apply damage to entities in explosion radius
                voidHydra.dimension
                    .getEntities({
                    location: targetPos,
                    maxDistance: ATTACK_CONFIG.EXPLOSION_RADIUS,
                    excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
                    excludeFamilies: ["void_hydra", "boss"]
                })
                    .forEach((entity) => {
                    entity.applyDamage(damage, {
                        cause: EntityDamageCause.entityAttack,
                        damagingEntity: voidHydra
                    });
                    // Apply knockback
                    try {
                        const knockbackDirection = {
                            x: entity.location.x - targetPos.x,
                            y: 0.4,
                            z: entity.location.z - targetPos.z
                        };
                        const magnitude = Math.sqrt(knockbackDirection.x ** 2 + knockbackDirection.z ** 2);
                        if (magnitude > 0) {
                            knockbackDirection.x = (knockbackDirection.x / magnitude) * 1.0;
                            knockbackDirection.z = (knockbackDirection.z / magnitude) * 1.0;
                        }
                        entity.applyKnockback(knockbackDirection.x, knockbackDirection.z, 1.0, 0.4);
                    }
                    catch (knockbackError) {
                        try {
                            const impulse = {
                                x: (entity.location.x - targetPos.x) * 0.4,
                                y: 0.3,
                                z: (entity.location.z - targetPos.z) * 0.4
                            };
                            entity.applyImpulse(impulse);
                        }
                        catch (impulseError) {
                            // Ignore if both methods fail
                        }
                    }
                });
                // Spawn additional fire particles
                for (let j = 0; j < 8; j++) {
                    const angle = (j / 8) * Math.PI * 2;
                    const radius = ATTACK_CONFIG.EXPLOSION_RADIUS * 0.7;
                    const particlePos = {
                        x: targetPos.x + Math.cos(angle) * radius,
                        y: targetPos.y + Math.random() * 2,
                        z: targetPos.z + Math.sin(angle) * radius
                    };
                    voidHydra.dimension.spawnParticle("minecraft:lava_particle", particlePos);
                }
            }
            catch (error) {
                // Handle errors silently
            }
        }, 20); // 1 second delay for meteor to "fall"
    }
    catch (error) {
        // Handle errors silently
    }
}
