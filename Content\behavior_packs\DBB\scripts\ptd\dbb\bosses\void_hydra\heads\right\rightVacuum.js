import { EntityDamageCause, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../../general_attacks/attackDamages";
/**
 * Attack timing constants for right vacuum attack
 */
const VACUUM_START_TIMING = 30; // Start vacuum effect at tick 30
const VACUUM_END_TIMING = 120; // End vacuum effect at tick 120
const ANIMATION_TIME = 150; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes
/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
    /** Range of the vacuum effect */
    RANGE: 20,
    /** Pull strength towards the void hydra */
    PULL_STRENGTH: 0.15,
    /** Damage interval in ticks */
    DAMAGE_INTERVAL: 10
};
/**
 * Executes the right vacuum attack for the Void Hydra
 * Creates a vacuum effect that pulls entities towards the hydra and deals continuous damage
 *
 * @param voidHydra The void hydra entity
 */
export function executeRightVacuumAttack(voidHydra) {
    let vacuumInterval;
    // Start vacuum effect at tick 30
    let vacuumStartTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(vacuumStartTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "right_vacuum") {
                // Start continuous vacuum effect
                let currentTick = VACUUM_START_TIMING;
                vacuumInterval = system.runInterval(() => {
                    try {
                        const isDead = voidHydra.getProperty("ptd_dbb:dead");
                        if (isDead || voidHydra.getProperty("ptd_dbb:attack") !== "right_vacuum") {
                            system.clearRun(vacuumInterval);
                            return;
                        }
                        if (currentTick <= VACUUM_END_TIMING) {
                            performVacuumEffect(voidHydra, currentTick);
                            currentTick++;
                        }
                        else {
                            system.clearRun(vacuumInterval);
                        }
                    }
                    catch (error) {
                        system.clearRun(vacuumInterval);
                    }
                }, 1);
            }
        }
        catch (error) {
            system.clearRun(vacuumStartTiming);
        }
    }, VACUUM_START_TIMING);
    // Reset attack and start cooldown after animation completes
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                if (vacuumInterval)
                    system.clearRun(vacuumInterval);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "right_vacuum") {
                voidHydra.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    // End cooldown after cooldown time
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            voidHydra.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
/**
 * Performs the vacuum effect for a specific tick
 * @param voidHydra The void hydra entity
 * @param currentTick The current tick of the vacuum effect
 */
function performVacuumEffect(voidHydra, currentTick) {
    try {
        const origin = voidHydra.location;
        const damage = VOID_HYDRA_ATTACK_DAMAGES.right_vacuum.damage;
        // Get all entities within range
        const entities = voidHydra.dimension.getEntities({
            location: origin,
            maxDistance: ATTACK_CONFIG.RANGE,
            excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
            excludeFamilies: ["void_hydra", "boss"]
        });
        entities.forEach((entity) => {
            // Calculate pull direction towards the void hydra
            const pullDirection = {
                x: origin.x - entity.location.x,
                y: origin.y - entity.location.y + 1, // Slight upward pull
                z: origin.z - entity.location.z
            };
            // Normalize the direction
            const magnitude = Math.sqrt(pullDirection.x ** 2 + pullDirection.y ** 2 + pullDirection.z ** 2);
            if (magnitude > 0) {
                pullDirection.x = (pullDirection.x / magnitude) * ATTACK_CONFIG.PULL_STRENGTH;
                pullDirection.y = (pullDirection.y / magnitude) * ATTACK_CONFIG.PULL_STRENGTH * 0.5; // Reduced vertical pull
                pullDirection.z = (pullDirection.z / magnitude) * ATTACK_CONFIG.PULL_STRENGTH;
                // Apply pull effect
                try {
                    entity.applyImpulse(pullDirection);
                }
                catch (impulseError) {
                    // Ignore if impulse fails
                }
            }
            // Apply damage at intervals
            if ((currentTick - VACUUM_START_TIMING) % ATTACK_CONFIG.DAMAGE_INTERVAL === 0) {
                entity.applyDamage(damage, {
                    cause: EntityDamageCause.entityAttack,
                    damagingEntity: voidHydra
                });
            }
            // Spawn vacuum particles around the entity
            if (currentTick % 3 === 0) { // Reduce particle frequency
                voidHydra.dimension.spawnParticle("minecraft:portal", entity.location);
            }
        });
        // Spawn central vacuum effect particles
        if (currentTick % 5 === 0) {
            const centerPos = {
                x: origin.x,
                y: origin.y + 2,
                z: origin.z
            };
            voidHydra.dimension.spawnParticle("minecraft:dragon_breath_trail", centerPos);
        }
    }
    catch (error) {
        // Handle errors silently
    }
}
