import { executeRightHeadAtomicAttack } from "./heads/right/attacks/rightAtomic";
/**
 * Handles right head attack mechanics
 * @param rightHead The right head entity
 */
export function rightHeadMechanics(rightHead) {
    try {
        // Skip if entity is not valid
        if (!rightHead || rightHead.typeId !== "ptd_dbb:void_hydra_right_head")
            return;
        // Skip if entity is spawning or dead
        const isSpawning = rightHead.getProperty("ptd_dbb:spawning");
        const isDead = rightHead.getProperty("ptd_dbb:dead");
        if (isSpawning || isDead)
            return;
        // Handle attack mechanics
        const currentAttack = rightHead.getProperty("ptd_dbb:attack");
        const isCoolingDown = rightHead.getProperty("ptd_dbb:cooling_down");
        if (currentAttack === "right_atomic" && !isCoolingDown) {
            executeRightHeadAtomicAttack(rightHead);
        }
    }
    catch (error) {
        // Handle errors silently
    }
}
