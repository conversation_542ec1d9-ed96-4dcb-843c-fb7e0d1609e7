import { EntityDamageCause, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getTarget } from "../../general_mechanics/targetUtils";
const MISSILE_LAUNCH_TIMING = 85;
const ANIMATION_TIME = 170;
const COOLDOWN_TIME = 20;
const ATTACK_CONFIG = {
    MISSILE_COUNT: 3,
    EXPLOSION_RADIUS: 4,
    SPAWN_RANGE: 6,
};
export function executeLeftMissileAttack(voidHydra) {
    let missileLaunchTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(missileLaunchTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "left_missile") {
                launchMissiles(voidHydra);
            }
        }
        catch (error) {
            system.clearRun(missileLaunchTiming);
        }
    }, MISSILE_LAUNCH_TIMING);
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "left_missile") {
                voidHydra.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            voidHydra.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
function launchMissiles(voidHydra) {
    try {
        const target = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
        if (!target)
            return;
        const targetPos = target.location;
        const damage = VOID_HYDRA_ATTACK_DAMAGES.left_missile.damage;
        for (let i = 0; i < ATTACK_CONFIG.MISSILE_COUNT; i++) {
            const angle = (Math.PI * 2 * i) / ATTACK_CONFIG.MISSILE_COUNT;
            const distance = Math.random() * ATTACK_CONFIG.SPAWN_RANGE;
            const missileTargetPos = {
                x: targetPos.x + Math.cos(angle) * distance,
                y: targetPos.y,
                z: targetPos.z + Math.sin(angle) * distance
            };
            const delay = i * 8;
            system.runTimeout(() => {
                try {
                    const isDead = voidHydra.getProperty("ptd_dbb:dead");
                    if (isDead || voidHydra.getProperty("ptd_dbb:attack") !== "left_missile") {
                        return;
                    }
                    // Warning effect
                    voidHydra.dimension.spawnParticle("minecraft:lava_particle", {
                        x: missileTargetPos.x,
                        y: missileTargetPos.y + 10,
                        z: missileTargetPos.z
                    });
                    // Impact after delay
                    system.runTimeout(() => {
                        try {
                            voidHydra.dimension.spawnParticle("minecraft:huge_explosion_emitter", missileTargetPos);
                            voidHydra.dimension
                                .getEntities({
                                location: missileTargetPos,
                                maxDistance: ATTACK_CONFIG.EXPLOSION_RADIUS,
                                excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
                                excludeFamilies: ["void_hydra", "boss"]
                            })
                                .forEach((entity) => {
                                entity.applyDamage(damage, {
                                    cause: EntityDamageCause.entityAttack,
                                    damagingEntity: voidHydra
                                });
                                try {
                                    const knockbackDirection = {
                                        x: entity.location.x - missileTargetPos.x,
                                        y: 0.4,
                                        z: entity.location.z - missileTargetPos.z
                                    };
                                    const magnitude = Math.sqrt(knockbackDirection.x ** 2 + knockbackDirection.z ** 2);
                                    if (magnitude > 0) {
                                        knockbackDirection.x = (knockbackDirection.x / magnitude) * 1.0;
                                        knockbackDirection.z = (knockbackDirection.z / magnitude) * 1.0;
                                    }
                                    entity.applyKnockback(knockbackDirection.x, knockbackDirection.z, 1.0, 0.4);
                                }
                                catch (knockbackError) {
                                    try {
                                        const impulse = {
                                            x: (entity.location.x - missileTargetPos.x) * 0.4,
                                            y: 0.3,
                                            z: (entity.location.z - missileTargetPos.z) * 0.4
                                        };
                                        entity.applyImpulse(impulse);
                                    }
                                    catch (impulseError) {
                                        // Ignore if both methods fail
                                    }
                                }
                            });
                        }
                        catch (error) {
                            // Handle errors silently
                        }
                    }, 15);
                }
                catch (error) {
                    // Handle errors silently
                }
            }, delay);
        }
    }
    catch (error) {
        // Handle errors silently
    }
}
